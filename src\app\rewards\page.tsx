'use client'

import { useState, useEffect } from 'react'
import { Mountain, Coins, Trophy, Gift, Star, Users, MessageSquare, ArrowLeft, Award, Target, Calendar, Crown, Zap } from 'lucide-react'
import Link from 'next/link'
import { useWallet } from '@/components/providers/WalletProvider'

interface NFT {
  id: string
  name: string
  description: string
  image: string
  trailName: string
  completedAt: string
  rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary'
}

interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  unlockedAt?: string
  progress?: number
  maxProgress?: number
}

interface TokenTransaction {
  id: string
  type: 'earned' | 'spent'
  amount: number
  description: string
  date: string
}

export default function RewardsPage() {
  const { connected, trekBalance } = useWallet()
  const [selectedTab, setSelectedTab] = useState('overview')
  const [nfts, setNfts] = useState<NFT[]>([])
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [tokenHistory, setTokenHistory] = useState<TokenTransaction[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadRewardsData()
  }, [])

  const loadRewardsData = () => {
    // Load sample data (in production, this would come from blockchain/API)
    const sampleNFTs: NFT[] = [
      {
        id: 'nft_1',
        name: 'Ella Rock Explorer',
        description: 'Completed the scenic Ella Rock trail',
        image: '/images/nfts/ella-rock.jpg',
        trailName: 'Ella Rock Trail',
        completedAt: '2024-01-15',
        rarity: 'Common'
      },
      {
        id: 'nft_2',
        name: 'Adams Peak Conqueror',
        description: 'Reached the sacred summit of Adams Peak',
        image: '/images/nfts/adams-peak.jpg',
        trailName: 'Adams Peak Trail',
        completedAt: '2024-01-10',
        rarity: 'Epic'
      }
    ]

    const sampleAchievements: Achievement[] = [
      {
        id: 'first_trail',
        name: 'First Steps',
        description: 'Complete your first trail',
        icon: '🥾',
        unlockedAt: '2024-01-15'
      },
      {
        id: 'distance_10k',
        name: 'Distance Walker',
        description: 'Walk 10km in total',
        icon: '🚶',
        progress: 8.2,
        maxProgress: 10
      },
      {
        id: 'early_bird',
        name: 'Early Bird',
        description: 'Complete 5 trails before 8 AM',
        icon: '🌅',
        progress: 2,
        maxProgress: 5
      }
    ]

    const sampleTokenHistory: TokenTransaction[] = [
      {
        id: 'tx_1',
        type: 'earned',
        amount: 75,
        description: 'Completed Ella Rock Trail',
        date: '2024-01-15'
      },
      {
        id: 'tx_2',
        type: 'earned',
        amount: 100,
        description: 'Completed Adams Peak Trail',
        date: '2024-01-10'
      },
      {
        id: 'tx_3',
        type: 'spent',
        amount: 50,
        description: 'Upgraded to Premium (1 month)',
        date: '2024-01-05'
      }
    ]

    setNfts(sampleNFTs)
    setAchievements(sampleAchievements)
    setTokenHistory(sampleTokenHistory)
    setLoading(false)
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Common': return 'bg-gray-100 text-gray-800'
      case 'Rare': return 'bg-blue-100 text-blue-800'
      case 'Epic': return 'bg-purple-100 text-purple-800'
      case 'Legendary': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const earnRewards = [
    {
      icon: Mountain,
      title: 'Complete Trails',
      description: 'Earn 50-100 TREK tokens for each trail you complete',
      reward: '50-100 TREK',
      color: 'green'
    },
    {
      icon: MessageSquare,
      title: 'Submit Reviews',
      description: 'Share your experience and earn tokens for helpful reviews',
      reward: '10 TREK',
      color: 'blue'
    },
    {
      icon: Users,
      title: 'Invite Friends',
      description: 'Bring friends to VinTrek and earn bonus rewards',
      reward: '100 TREK',
      color: 'purple'
    },
    {
      icon: Star,
      title: 'Special Events',
      description: 'Participate in seasonal events and challenges',
      reward: 'Variable',
      color: 'yellow'
    }
  ]

  const redeemOptions = [
    {
      title: 'Premium Membership',
      description: 'Unlock exclusive trails, offline maps, and AR experiences',
      cost: '300 TREK',
      duration: '1 month',
      features: ['Exclusive trails', 'Offline maps', 'AR experiences', 'Priority support']
    },
    {
      title: 'Trail Gear Discount',
      description: 'Get 20% off hiking gear from partner stores',
      cost: '150 TREK',
      duration: 'One-time use',
      features: ['20% discount', 'Partner stores', 'Valid for 30 days']
    },
    {
      title: 'Campsite Booking Credit',
      description: 'Credit towards your next campsite booking',
      cost: '200 TREK',
      duration: 'One-time use',
      features: ['₨1000 credit', 'Any campsite', 'Valid for 60 days']
    }
  ]

  const getIconColor = (color: string) => {
    switch (color) {
      case 'green': return 'text-green-600 bg-green-100'
      case 'blue': return 'text-blue-600 bg-blue-100'
      case 'purple': return 'text-purple-600 bg-purple-100'
      case 'yellow': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <ArrowLeft className="h-5 w-5 text-gray-600" />
              <Mountain className="h-8 w-8 text-green-600" />
              <span className="text-2xl font-bold text-gray-900">VinTrek</span>
            </Link>
            <div className="text-sm text-gray-600">
              Rewards & Achievements
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">TREK Token Rewards</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Earn TREK tokens for your outdoor adventures and redeem them for exclusive benefits, 
            gear discounts, and premium features.
          </p>
        </div>

        {/* Token Balance Display */}
        <div className="bg-white rounded-lg shadow-md p-6 max-w-sm mx-auto mb-8">
          <div className="flex items-center justify-center space-x-3 mb-2">
            <Coins className="h-6 w-6 text-yellow-500" />
            <span className="text-2xl font-bold text-gray-900">{trekBalance || 1250}</span>
            <span className="text-lg text-gray-600">TREK</span>
          </div>
          <p className="text-sm text-gray-600 text-center">Your current balance</p>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-md flex space-x-1">
            {[
              { id: 'overview', label: 'Overview', icon: Trophy },
              { id: 'earn', label: 'Earn', icon: Coins },
              { id: 'nfts', label: 'NFTs', icon: Star },
              { id: 'achievements', label: 'Achievements', icon: Award }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedTab === tab.id
                    ? 'bg-green-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Overview Tab */}
        {selectedTab === 'overview' && (
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            {/* Stats Cards */}
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <Trophy className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{nfts.length}</div>
              <div className="text-sm text-gray-600">NFTs Collected</div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <Award className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{achievements.filter(a => a.unlockedAt).length}</div>
              <div className="text-sm text-gray-600">Achievements Unlocked</div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <Target className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{tokenHistory.filter(t => t.type === 'earned').reduce((sum, t) => sum + t.amount, 0)}</div>
              <div className="text-sm text-gray-600">Total TREK Earned</div>
            </div>
          </div>
        )}

        {/* NFTs Tab */}
        {selectedTab === 'nfts' && (
          <div>
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Your NFT Collection</h2>
              <p className="text-gray-600">
                Unique digital certificates for your trail completions
              </p>
            </div>

            {nfts.length === 0 ? (
              <div className="bg-white rounded-lg shadow-md p-8 text-center">
                <Star className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No NFTs Yet</h3>
                <p className="text-gray-600 mb-4">Complete trails to earn your first NFT certificate!</p>
                <Link href="/trails" className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors inline-block">
                  Browse Trails
                </Link>
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {nfts.map((nft) => (
                  <div key={nft.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="h-48 bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
                      <Mountain className="h-16 w-16 text-white" />
                    </div>
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-gray-900">{nft.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(nft.rarity)}`}>
                          {nft.rarity}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{nft.description}</p>
                      <div className="text-xs text-gray-500">
                        Completed: {new Date(nft.completedAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Achievements Tab */}
        {selectedTab === 'achievements' && (
          <div>
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Achievements</h2>
              <p className="text-gray-600">
                Track your progress and unlock special rewards
              </p>
            </div>

            <div className="space-y-4">
              {achievements.map((achievement) => (
                <div key={achievement.id} className={`bg-white rounded-lg shadow-md p-6 ${achievement.unlockedAt ? 'border-l-4 border-green-500' : 'opacity-75'}`}>
                  <div className="flex items-center space-x-4">
                    <div className="text-3xl">{achievement.icon}</div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{achievement.name}</h3>
                      <p className="text-sm text-gray-600">{achievement.description}</p>
                      {achievement.progress !== undefined && achievement.maxProgress && (
                        <div className="mt-2">
                          <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{achievement.progress}/{achievement.maxProgress}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-green-600 h-2 rounded-full"
                              style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                      {achievement.unlockedAt && (
                        <div className="text-xs text-green-600 mt-1">
                          ✓ Unlocked on {new Date(achievement.unlockedAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Earn Rewards Tab */}
        {selectedTab === 'earn' && (
          <div>
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">How to Earn TREK Tokens</h2>
              <p className="text-gray-600">
                Complete activities and engage with the VinTrek community to earn tokens
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6 mb-8">
              {earnRewards.map((reward, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-full ${getIconColor(reward.color)}`}>
                      <reward.icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{reward.title}</h3>
                      <p className="text-gray-600 mb-3">{reward.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Reward:</span>
                        <span className="font-semibold text-green-600">{reward.reward}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Token Economy Info */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">About TREK Tokens</h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <Coins className="h-12 w-12 text-yellow-500 mx-auto mb-3" />
                  <h4 className="font-semibold text-gray-900 mb-2">Utility Token</h4>
                  <p className="text-sm text-gray-600">
                    TREK is a fungible utility token built on Cardano blockchain
                  </p>
                </div>
                <div className="text-center">
                  <Trophy className="h-12 w-12 text-green-500 mx-auto mb-3" />
                  <h4 className="font-semibold text-gray-900 mb-2">Earn & Trade</h4>
                  <p className="text-sm text-gray-600">
                    Earn tokens through activities or trade on Cardano DEXs
                  </p>
                </div>
                <div className="text-center">
                  <Gift className="h-12 w-12 text-purple-500 mx-auto mb-3" />
                  <h4 className="font-semibold text-gray-900 mb-2">Real Value</h4>
                  <p className="text-sm text-gray-600">
                    Redeem for premium features, discounts, and exclusive content
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Redeem Tokens Tab */}
        {selectedTab === 'redeem' && (
          <div>
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Redeem Your TREK Tokens</h2>
              <p className="text-gray-600">
                Use your earned tokens to unlock premium features and exclusive benefits
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              {redeemOptions.map((option, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                  <div className="text-center mb-4">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{option.title}</h3>
                    <p className="text-gray-600 mb-4">{option.description}</p>
                    <div className="text-2xl font-bold text-green-600 mb-1">{option.cost}</div>
                    <div className="text-sm text-gray-500">{option.duration}</div>
                  </div>
                  
                  <div className="space-y-2 mb-6">
                    {option.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <button 
                    type="button"
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                    onClick={() => alert('Redemption feature coming soon! Connect your wallet to enable token redemption.')}
                  >
                    Redeem Now
                  </button>
                </div>
              ))}
            </div>

            {/* Trading Info */}
            <div className="mt-8 bg-blue-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Trade TREK Tokens</h3>
              <p className="text-gray-600 mb-4">
                TREK tokens are tradeable on Cardano decentralized exchanges. Current market value: approximately $0.067 per token.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button 
                  type="button"
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  onClick={() => window.open('https://minswap.org', '_blank')}
                >
                  Trade on Minswap
                </button>
                <button 
                  type="button"
                  className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                  onClick={() => window.open('https://sundaeswap.finance', '_blank')}
                >
                  Trade on SundaeSwap
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
