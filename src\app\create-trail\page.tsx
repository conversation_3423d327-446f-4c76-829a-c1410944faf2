'use client'

import { useState } from 'react'
import { Mountain, ArrowLeft, Upload, MapPin, Clock, TrendingUp, Save, Eye } from 'lucide-react'
import Link from 'next/link'
import { useWallet } from '@/components/providers/WalletProvider'
import { trailService } from '@/services/trailService'

interface TrailFormData {
  name: string
  location: string
  difficulty: 'Easy' | 'Moderate' | 'Hard' | 'Expert'
  distance: string
  duration: string
  description: string
  features: string[]
  coordinates: Array<{ lat: number; lng: number }>
  startPoint?: { lat: number; lng: number }
  endPoint?: { lat: number; lng: number }
}

export default function CreateTrailPage() {
  const { connected, address } = useWallet()
  const [formData, setFormData] = useState<TrailFormData>({
    name: '',
    location: '',
    difficulty: 'Easy',
    distance: '',
    duration: '',
    description: '',
    features: [],
    coordinates: []
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState('')
  const [submitSuccess, setSubmitSuccess] = useState(false)

  const availableFeatures = [
    'GPS Tracking',
    'NFT Certificate',
    'TREK Rewards',
    'Waterfall',
    'Mountain Views',
    'Forest Trail',
    'Wildlife Viewing',
    'Photography',
    'Sunrise Views',
    'Historical Sites',
    'Rock Climbing',
    'Swimming'
  ]

  const handleInputChange = (field: keyof TrailFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleFeatureToggle = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }))
  }

  const handleGPSUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Simulate GPS file parsing
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          // For demo purposes, generate some sample coordinates
          const sampleCoordinates = [
            { lat: 6.8721, lng: 81.0462 },
            { lat: 6.8731, lng: 81.0472 },
            { lat: 6.8741, lng: 81.0482 },
            { lat: 6.8751, lng: 81.0492 }
          ]
          
          setFormData(prev => ({
            ...prev,
            coordinates: sampleCoordinates,
            startPoint: sampleCoordinates[0],
            endPoint: sampleCoordinates[sampleCoordinates.length - 1]
          }))
        } catch (error) {
          console.error('Error parsing GPS file:', error)
        }
      }
      reader.readAsText(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!connected || !address) {
      setSubmitError('Please connect your wallet to create a trail.')
      return
    }

    if (!formData.name || !formData.location || !formData.description) {
      setSubmitError('Please fill in all required fields.')
      return
    }

    setIsSubmitting(true)
    setSubmitError('')

    try {
      // Simulate trail creation process
      console.log('🏔️ Creating new trail...')
      
      // Step 1: Validate trail data
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Step 2: Generate trail ID
      const trailId = `user-trail-${Date.now()}`
      
      // Step 3: Create trail object
      const newTrail = {
        id: trailId,
        name: formData.name,
        location: formData.location,
        difficulty: formData.difficulty,
        distance: formData.distance,
        duration: formData.duration,
        description: formData.description,
        features: formData.features,
        coordinates: formData.coordinates,
        startPoint: formData.startPoint,
        endPoint: formData.endPoint,
        price: 0, // User-contributed trails are free
        rating: 0,
        reviews: 0,
        available: true,
        image: '/images/trails/user-contributed.jpg',
        rewards: {
          trekTokens: Math.floor(parseFloat(formData.distance) * 5) || 25,
          nftCertificate: true,
          experiencePoints: Math.floor(parseFloat(formData.distance) * 50) || 250
        },
        isPremiumOnly: false,
        isUserContributed: true,
        contributedBy: address,
        contributedByName: `Hiker_${address.slice(-6)}`,
        createdBy: 'user',
        verified: false, // Needs verification
        createdAt: Date.now()
      }
      
      // Step 4: Store trail locally (simulate blockchain storage)
      const existingTrails = JSON.parse(localStorage.getItem('vintrek_user_trails') || '[]')
      existingTrails.push(newTrail)
      localStorage.setItem('vintrek_user_trails', JSON.stringify(existingTrails))
      
      // Step 5: Simulate blockchain submission
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setSubmitSuccess(true)
      
      // Reset form
      setFormData({
        name: '',
        location: '',
        difficulty: 'Easy',
        distance: '',
        duration: '',
        description: '',
        features: [],
        coordinates: []
      })
      
    } catch (error) {
      console.error('Error creating trail:', error)
      setSubmitError('Failed to create trail. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!connected) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link href="/" className="flex items-center space-x-2">
                <ArrowLeft className="h-5 w-5 text-gray-600" />
                <Mountain className="h-8 w-8 text-green-600" />
                <span className="text-2xl font-bold text-gray-900">VinTrek</span>
              </Link>
            </div>
          </div>
        </header>

        <div className="max-w-2xl mx-auto px-4 py-16 text-center">
          <Mountain className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Connect Your Wallet</h2>
          <p className="text-gray-600 mb-6">
            You need to connect your Cardano wallet to create and contribute trails to the VinTrek community.
          </p>
          <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
            Connect Wallet
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <ArrowLeft className="h-5 w-5 text-gray-600" />
              <Mountain className="h-8 w-8 text-green-600" />
              <span className="text-2xl font-bold text-gray-900">VinTrek</span>
            </Link>
            <div className="text-sm text-gray-600">
              Create Trail
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Create New Trail</h1>
          <p className="text-gray-600">
            Share your favorite hiking route with the VinTrek community and earn TREK tokens.
          </p>
        </div>

        {submitSuccess && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Mountain className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">
                  Trail created successfully! It will be reviewed and added to the platform soon.
                </p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trail Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., Hidden Waterfall Trail"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Location *
              </label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="e.g., Kandy, Sri Lanka"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
          </div>

          {/* Trail Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Difficulty
              </label>
              <select
                value={formData.difficulty}
                onChange={(e) => handleInputChange('difficulty', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="Easy">Easy</option>
                <option value="Moderate">Moderate</option>
                <option value="Hard">Hard</option>
                <option value="Expert">Expert</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Distance
              </label>
              <input
                type="text"
                value={formData.distance}
                onChange={(e) => handleInputChange('distance', e.target.value)}
                placeholder="e.g., 5.2 km"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duration
              </label>
              <input
                type="text"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', e.target.value)}
                placeholder="e.g., 2-3 hours"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the trail, what hikers can expect, notable features, difficulty points, etc."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            />
          </div>

          {/* Features */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trail Features
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {availableFeatures.map((feature) => (
                <label key={feature} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.features.includes(feature)}
                    onChange={() => handleFeatureToggle(feature)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="text-sm text-gray-700">{feature}</span>
                </label>
              ))}
            </div>
          </div>

          {/* GPS Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              GPS Track (Optional)
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-2">
                Upload a GPX file with your trail route
              </p>
              <input
                type="file"
                accept=".gpx,.kml,.json"
                onChange={handleGPSUpload}
                className="hidden"
                id="gps-upload"
              />
              <label
                htmlFor="gps-upload"
                className="bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer"
              >
                Choose File
              </label>
              {formData.coordinates.length > 0 && (
                <p className="text-sm text-green-600 mt-2">
                  ✓ GPS track uploaded ({formData.coordinates.length} points)
                </p>
              )}
            </div>
          </div>

          {/* Error Message */}
          {submitError && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">{submitError}</p>
            </div>
          )}

          {/* Submit Buttons */}
          <div className="flex space-x-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 ${
                isSubmitting
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              <Save className="h-5 w-5" />
              <span>{isSubmitting ? 'Creating Trail...' : 'Create Trail'}</span>
            </button>
            
            <Link
              href="/trails"
              className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors flex items-center space-x-2"
            >
              <Eye className="h-5 w-5" />
              <span>Preview</span>
            </Link>
          </div>
        </form>
      </div>
    </div>
  )
}
