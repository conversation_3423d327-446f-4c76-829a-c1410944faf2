'use client'

import { useState, useEffect } from 'react'
import { 
  Wallet, 
  RefreshCw, 
  AlertCircle, 
  CheckCircle, 
  XCircle, 
  Settings,
  Eye,
  EyeOff,
  Copy,
  ExternalLink
} from 'lucide-react'
import { useWallet } from '@/components/providers/WalletProvider'

interface WalletTestResult {
  walletName: string
  available: boolean
  connected: boolean
  address?: string
  balance?: string
  trekBalance?: number
  networkId?: number
  error?: string
}

export function WalletTester() {
  const { connected, address, balance, trekBalance, connect, disconnect, getAvailableWallets } = useWallet()
  const [testResults, setTestResults] = useState<WalletTestResult[]>([])
  const [isTestingAll, setIsTestingAll] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [isDemoMode, setIsDemoMode] = useState(true)

  useEffect(() => {
    setIsDemoMode(process.env.NEXT_PUBLIC_DEMO_MODE === 'true')
    scanWallets()
  }, [])

  const scanWallets = () => {
    const availableWallets = getAvailableWallets()
    const allWallets = ['lace', 'eternl', 'nami', 'flint', 'typhon', 'gerowallet', 'ccvault', 'yoroi']
    
    const results: WalletTestResult[] = allWallets.map(walletName => ({
      walletName,
      available: availableWallets.includes(walletName),
      connected: false
    }))
    
    setTestResults(results)
  }

  const testWallet = async (walletName: string) => {
    try {
      await connect(walletName)
      
      // Update test results
      setTestResults(prev => prev.map(result => 
        result.walletName === walletName 
          ? {
              ...result,
              connected: true,
              address,
              balance,
              trekBalance,
              error: undefined
            }
          : result
      ))
    } catch (error) {
      setTestResults(prev => prev.map(result => 
        result.walletName === walletName 
          ? {
              ...result,
              connected: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          : result
      ))
    }
  }

  const testAllWallets = async () => {
    setIsTestingAll(true)
    const availableWallets = testResults.filter(r => r.available)
    
    for (const wallet of availableWallets) {
      try {
        await testWallet(wallet.walletName)
        await new Promise(resolve => setTimeout(resolve, 1000)) // Delay between tests
      } catch (error) {
        console.error(`Failed to test ${wallet.walletName}:`, error)
      }
    }
    
    setIsTestingAll(false)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const getWalletIcon = (walletName: string) => {
    const icons: Record<string, string> = {
      lace: '🃏',
      eternl: '♾️',
      nami: '🌊',
      flint: '🔥',
      typhon: '🌪️',
      gerowallet: '⚙️',
      ccvault: '🏦',
      yoroi: '🏯'
    }
    return icons[walletName] || '💳'
  }

  const getStatusIcon = (result: WalletTestResult) => {
    if (!result.available) return <XCircle className="h-5 w-5 text-red-500" />
    if (result.connected) return <CheckCircle className="h-5 w-5 text-green-500" />
    if (result.error) return <AlertCircle className="h-5 w-5 text-yellow-500" />
    return <Wallet className="h-5 w-5 text-gray-400" />
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Wallet className="h-6 w-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Wallet Connection Tester</h2>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            isDemoMode ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
          }`}>
            {isDemoMode ? '🎭 Demo Mode' : '🔗 Real Mode'}
          </span>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
          >
            {showDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
      </div>

      {/* Current Connection Status */}
      {connected && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="font-medium text-green-800">Wallet Connected</span>
          </div>
          {showDetails && (
            <div className="space-y-2 text-sm text-green-700">
              <div className="flex items-center space-x-2">
                <span>Address:</span>
                <code className="bg-green-100 px-2 py-1 rounded text-xs">
                  {address ? `${address.slice(0, 20)}...${address.slice(-10)}` : 'N/A'}
                </code>
                {address && (
                  <button onClick={() => copyToClipboard(address)} className="text-green-600 hover:text-green-800">
                    <Copy className="h-3 w-3" />
                  </button>
                )}
              </div>
              <div>ADA Balance: <span className="font-medium">{balance || 'N/A'}</span></div>
              <div>TREK Balance: <span className="font-medium">{trekBalance || 0} TREK</span></div>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-3 mb-6">
        <button
          onClick={scanWallets}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Scan Wallets</span>
        </button>
        
        <button
          onClick={testAllWallets}
          disabled={isTestingAll}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
            isTestingAll
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          <Settings className={`h-4 w-4 ${isTestingAll ? 'animate-spin' : ''}`} />
          <span>{isTestingAll ? 'Testing...' : 'Test Available'}</span>
        </button>

        {connected && (
          <button
            onClick={disconnect}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <XCircle className="h-4 w-4" />
            <span>Disconnect</span>
          </button>
        )}
      </div>

      {/* Wallet Test Results */}
      <div className="space-y-3">
        <h3 className="text-lg font-medium text-gray-900">Wallet Detection Results</h3>
        
        {testResults.map((result) => (
          <div key={result.walletName} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{getWalletIcon(result.walletName)}</span>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium capitalize">{result.walletName}</span>
                    {getStatusIcon(result)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {!result.available && 'Not installed'}
                    {result.available && !result.connected && !result.error && 'Available'}
                    {result.connected && 'Connected'}
                    {result.error && 'Connection failed'}
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2">
                {result.available && !result.connected && (
                  <button
                    onClick={() => testWallet(result.walletName)}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                  >
                    Test
                  </button>
                )}
                
                {!result.available && (
                  <a
                    href={`https://chrome.google.com/webstore/search/${result.walletName}%20cardano`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-1 px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors"
                  >
                    <span>Install</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                )}
              </div>
            </div>
            
            {showDetails && result.error && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                <strong>Error:</strong> {result.error}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Testing Instructions:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Install a Cardano wallet extension (Lace, Eternl, Nami, or Flint recommended)</li>
          <li>• Set up your wallet with testnet funds for testing</li>
          <li>• Click "Test" next to an available wallet to connect</li>
          <li>• Use the wallet for real blockchain transactions in VinTrek</li>
        </ul>
      </div>
    </div>
  )
}
