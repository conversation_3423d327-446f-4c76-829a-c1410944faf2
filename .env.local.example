# VinTrek Environment Configuration

# Blockfrost API Configuration (Get from https://blockfrost.io)
# For testnet: Create account → Create project → Copy project ID
NEXT_PUBLIC_BLOCKFROST_PROJECT_ID=testnetYourProjectIdHere
NEXT_PUBLIC_BLOCKFROST_API_URL=https://cardano-testnet.blockfrost.io/api/v0

# Cardano Network Configuration
NEXT_PUBLIC_CARDANO_NETWORK=testnet
NEXT_PUBLIC_NETWORK_ID=0

# For Production (Mainnet) - Comment out testnet and uncomment these:
# NEXT_PUBLIC_BLOCKFROST_PROJECT_ID=mainnetYourProjectIdHere
# NEXT_PUBLIC_BLOCKFROST_API_URL=https://cardano-mainnet.blockfrost.io/api/v0
# NEXT_PUBLIC_CARDANO_NETWORK=mainnet
# NEXT_PUBLIC_NETWORK_ID=1

# Smart Contract Configuration
NEXT_PUBLIC_NFT_POLICY_ID=your_nft_policy_id_here
NEXT_PUBLIC_TOKEN_POLICY_ID=your_token_policy_id_here
NEXT_PUBLIC_SCRIPT_ADDRESS=your_script_address_here

# Application Configuration
NEXT_PUBLIC_APP_NAME=VinTrek
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database Configuration (if using)
DATABASE_URL=your_database_url_here

# API Keys
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# IPFS Configuration (for NFT metadata)
NEXT_PUBLIC_IPFS_GATEWAY=https://ipfs.io/ipfs/
PINATA_API_KEY=your_pinata_api_key_here
PINATA_SECRET_API_KEY=your_pinata_secret_key_here

# Email Configuration (for notifications)
SMTP_HOST=your_smtp_host_here
SMTP_PORT=587
SMTP_USER=your_smtp_user_here
SMTP_PASS=your_smtp_password_here

# Security
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000
