# 🔗 **VinTrek Real Wallet Integration Setup Guide**

This guide will help you set up real Cardano wallet connections and blockchain features for VinTrek.

---

## 🎯 **Overview: Demo vs Real Mode**

VinTrek supports two modes:

### **🎭 Demo Mode (Current Default)**
- ✅ Simulated wallet connections
- ✅ Mock blockchain transactions  
- ✅ Sample data for all features
- ✅ No real ADA or tokens required
- ✅ Perfect for demonstrations

### **🔗 Real Mode (Production Ready)**
- ✅ Actual Cardano wallet connections
- ✅ Real blockchain transactions
- ✅ Live token balances and NFTs
- ✅ Testnet/Mainnet support
- ⚠️ Requires setup and configuration

---

## 🚀 **Step-by-Step Real Mode Setup**

### **Step 1: Install a Cardano Wallet**

Choose and install one of these wallets:

#### **Recommended Wallets:**

1. **Lace Wallet** (Recommended for beginners)
   - 🌐 https://www.lace.io/
   - ✅ Official IOG wallet
   - ✅ User-friendly interface
   - ✅ Built-in DApp connector

2. **Eternl Wallet** (Advanced users)
   - 🌐 https://eternl.io/
   - ✅ Feature-rich
   - ✅ Advanced transaction features
   - ✅ Multi-account support

3. **Nami Wallet** (Lightweight)
   - 🌐 https://namiwallet.io/
   - ✅ Simple and fast
   - ✅ Good for basic operations

4. **Flint Wallet** (Mobile-first)
   - 🌐 https://flint-wallet.com/
   - ✅ Mobile-optimized
   - ✅ Clean interface

### **Step 2: Configure Wallet for Testnet**

1. **Open your wallet settings**
2. **Switch to Testnet/Preview network**
3. **Create or restore a testnet wallet**
4. **Save your seed phrase securely**

### **Step 3: Get Testnet ADA**

1. **Get your testnet address from your wallet**
2. **Visit the Cardano Testnet Faucet:**
   - 🌐 https://docs.cardano.org/cardano-testnet/tools/faucet
3. **Request testnet ADA (usually 1000 ADA)**
4. **Wait for confirmation (usually 1-2 minutes)**

### **Step 4: Get Blockfrost API Key**

1. **Visit Blockfrost.io:**
   - 🌐 https://blockfrost.io/
2. **Create a free account**
3. **Create a new project**
4. **Select "Cardano Testnet"**
5. **Copy your project ID (API key)**

### **Step 5: Configure VinTrek Environment**

1. **Update `.env.local` file:**

```bash
# Switch to real mode
NEXT_PUBLIC_DEMO_MODE=false

# Add your Blockfrost API key
NEXT_PUBLIC_BLOCKFROST_PROJECT_ID=testnet_your_actual_api_key_here

# Testnet configuration
NEXT_PUBLIC_BLOCKFROST_API_URL=https://cardano-testnet.blockfrost.io/api/v0
NEXT_PUBLIC_CARDANO_NETWORK=testnet
NEXT_PUBLIC_NETWORK_ID=0
```

2. **Restart the application:**
```bash
npm run dev
```

### **Step 6: Test Wallet Connection**

1. **Navigate to the Wallet Test page:**
   - 🌐 http://localhost:3000/wallet-test

2. **Use the Wallet Tester:**
   - Click "Scan Wallets" to detect installed wallets
   - Click "Test" next to your wallet
   - Approve the connection in your wallet
   - Verify address and balance display

3. **Check the Debug Info tab for detailed connection data**

---

## 🎯 **Features That Work with Real Wallets**

### **✅ Currently Functional with Real Wallets:**

1. **Wallet Connection & Authentication**
   - Real wallet detection and connection
   - Address and balance display
   - Network verification

2. **ADA Balance Queries**
   - Live ADA balance from blockchain
   - Transaction history (via Blockfrost)

3. **Basic Transaction Signing**
   - Transaction building with Mesh SDK
   - Wallet signature requests
   - Transaction submission

### **⚠️ Requires Additional Setup:**

1. **TREK Token Operations**
   - Needs TREK token policy ID
   - Requires token minting contract
   - Balance queries work if tokens exist

2. **NFT Minting**
   - Needs NFT minting contract deployment
   - Requires IPFS setup for metadata
   - Policy ID configuration

3. **Smart Contract Interactions**
   - Requires Aiken contract compilation
   - Contract deployment to testnet
   - Script address configuration

---

## 🔧 **Advanced Configuration**

### **For TREK Token Support:**

1. **Deploy TREK token minting contract**
2. **Update environment with policy ID:**
```bash
NEXT_PUBLIC_TOKEN_POLICY_ID=your_trek_token_policy_id
```

### **For NFT Minting:**

1. **Set up IPFS storage (Pinata recommended):**
```bash
PINATA_API_KEY=your_pinata_api_key
PINATA_SECRET_API_KEY=your_pinata_secret_key
```

2. **Deploy NFT minting contract**
3. **Update environment:**
```bash
NEXT_PUBLIC_NFT_POLICY_ID=your_nft_policy_id
NEXT_PUBLIC_SCRIPT_ADDRESS=your_script_address
```

### **For Mainnet (Production):**

1. **Get mainnet Blockfrost API key**
2. **Update environment:**
```bash
NEXT_PUBLIC_BLOCKFROST_PROJECT_ID=mainnet_your_api_key
NEXT_PUBLIC_BLOCKFROST_API_URL=https://cardano-mainnet.blockfrost.io/api/v0
NEXT_PUBLIC_CARDANO_NETWORK=mainnet
NEXT_PUBLIC_NETWORK_ID=1
```

---

## 🧪 **Testing Real Wallet Features**

### **Basic Wallet Testing:**

1. **Connection Test:**
   ```
   ✅ Wallet detects and connects
   ✅ Address displays correctly
   ✅ ADA balance shows real amount
   ✅ Network ID matches (0 for testnet)
   ```

2. **Transaction Test:**
   ```
   ✅ Can build simple transactions
   ✅ Wallet prompts for signature
   ✅ Transaction submits successfully
   ✅ Transaction appears on blockchain
   ```

### **VinTrek Feature Testing:**

1. **Trail Completion:**
   - Complete a trail in the app
   - Check if completion data is stored
   - Verify transaction on blockchain explorer

2. **Booking System:**
   - Book a trail with real wallet
   - Check transaction metadata
   - Verify booking confirmation

3. **Token Operations:**
   - Check TREK token balance (if tokens exist)
   - Test token reward transactions
   - Verify token transfers

---

## 🔍 **Troubleshooting**

### **Common Issues:**

1. **Wallet Not Detected:**
   - Ensure wallet extension is installed
   - Refresh the page
   - Check browser console for errors

2. **Connection Fails:**
   - Check wallet is unlocked
   - Verify network settings match
   - Try different wallet

3. **Balance Shows 0:**
   - Verify wallet has testnet ADA
   - Check Blockfrost API key is correct
   - Ensure network configuration matches

4. **Transactions Fail:**
   - Check wallet has sufficient ADA for fees
   - Verify transaction parameters
   - Check network connectivity

### **Debug Tools:**

1. **Wallet Test Page:**
   - 🌐 http://localhost:3000/wallet-test
   - Comprehensive wallet testing
   - Debug information display

2. **Browser Console:**
   - Check for error messages
   - Monitor transaction logs
   - Verify API responses

3. **Blockfrost Explorer:**
   - Verify transactions on blockchain
   - Check address balances
   - Monitor API usage

---

## 🎉 **Success Indicators**

### **Real Mode is Working When:**

✅ Wallet connects without simulation messages
✅ Real ADA balance displays from your wallet  
✅ Address matches your wallet address
✅ Transactions create real blockchain entries
✅ Blockfrost API calls return live data
✅ No "Demo mode" or "Mock" messages in console

### **Ready for Production When:**

✅ All wallet connections work reliably
✅ Token contracts deployed and configured
✅ NFT minting contracts operational  
✅ IPFS metadata storage working
✅ Mainnet configuration tested
✅ Security audit completed

---

## 📞 **Support & Resources**

### **Documentation:**
- Cardano Developer Portal: https://developers.cardano.org/
- Mesh SDK Docs: https://meshjs.dev/
- Blockfrost API: https://docs.blockfrost.io/

### **Community:**
- Cardano Stack Exchange: https://cardano.stackexchange.com/
- Mesh SDK Discord: https://discord.gg/mesh
- Cardano Developer Community: https://discord.gg/cardano

**VinTrek is now ready for real blockchain integration!** 🚀
