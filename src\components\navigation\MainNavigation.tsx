'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Mountain, 
  Home, 
  Map, 
  Trophy, 
  BarChart3, 
  Plus, 
  Calendar,
  Radio,
  Menu,
  X,
  Wallet,
  User
} from 'lucide-react'
import { useWallet } from '@/components/providers/WalletProvider'

export function MainNavigation() {
  const pathname = usePathname()
  const { connected, address, trekBalance } = useWallet()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const navigationItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/trails', label: 'Trails', icon: Map },
    { href: '/dashboard', label: 'Dashboard', icon: BarChart3 },
    { href: '/rewards', label: 'Rewards', icon: Trophy },
    { href: '/create-trail', label: 'Create', icon: Plus },
    { href: '/record', label: 'Record', icon: Radio },
    { href: '/bookings', label: 'Bookings', icon: Calendar },
  ]

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <nav className="bg-white shadow-lg border-b sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Mountain className="h-8 w-8 text-green-600" />
            <span className="text-2xl font-bold text-gray-900">VinTrek</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navigationItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isActive(item.href)
                    ? 'bg-green-100 text-green-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <item.icon className="h-4 w-4" />
                <span>{item.label}</span>
              </Link>
            ))}
          </div>

          {/* Wallet Section */}
          <div className="hidden md:flex items-center space-x-4">
            {connected ? (
              <div className="flex items-center space-x-3">
                {/* TREK Balance */}
                <div className="flex items-center space-x-1 bg-yellow-50 px-3 py-1 rounded-full">
                  <Trophy className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-700">
                    {trekBalance || 1250} TREK
                  </span>
                </div>
                
                {/* Wallet Address */}
                <div className="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-full">
                  <Wallet className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-700">
                    {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Connected'}
                  </span>
                </div>
              </div>
            ) : (
              <button className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
                <Wallet className="h-4 w-4" />
                <span>Connect Wallet</span>
              </button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <div className="space-y-2">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isActive(item.href)
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  <span>{item.label}</span>
                </Link>
              ))}
            </div>
            
            {/* Mobile Wallet Section */}
            <div className="mt-4 pt-4 border-t">
              {connected ? (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 px-3 py-2">
                    <Trophy className="h-5 w-5 text-yellow-600" />
                    <span className="text-sm font-medium text-gray-700">
                      {trekBalance || 1250} TREK Tokens
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 px-3 py-2">
                    <Wallet className="h-5 w-5 text-green-600" />
                    <span className="text-sm font-medium text-gray-700">
                      {address ? `${address.slice(0, 8)}...${address.slice(-6)}` : 'Connected'}
                    </span>
                  </div>
                </div>
              ) : (
                <button className="w-full flex items-center justify-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
                  <Wallet className="h-4 w-4" />
                  <span>Connect Wallet</span>
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
